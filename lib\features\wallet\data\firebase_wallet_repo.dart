import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:social_app_bloc_flutter/features/wallet/domain/entities/transaction.dart'
    as wallet_transaction;
import 'package:social_app_bloc_flutter/features/wallet/domain/entities/wallet.dart';
import 'package:social_app_bloc_flutter/features/wallet/domain/repos/wallet_repo.dart';

class FirebaseWalletRepo implements WalletRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;

  final CollectionReference walletsCollection = FirebaseFirestore.instance
      .collection('wallets_new');
  final CollectionReference transactionsCollection = FirebaseFirestore.instance
      .collection('transactions_new');

  @override
  Future<Wallet> getWallet(String userId) async {
    try {
      final walletDoc = await walletsCollection.doc(userId).get();
      if (walletDoc.exists) {
        return Wallet.fromJson(walletDoc.data() as Map<String, dynamic>);
      } else {
        // Create new wallet with 0 balance
        final newWallet = Wallet(userId: userId, balance: 2.0);
        await walletsCollection.doc(userId).set(newWallet.toJson());
        return newWallet;
      }
    } catch (e) {
      throw Exception("Failed to get wallet: $e");
    }
  }

  @override
  Future<void> addBalance(
    String userId,
    double amount,
    String description,
  ) async {
    try {
      // Get current wallet
      final wallet = await getWallet(userId);

      // Update balance
      final updatedWallet = wallet.copyWith(balance: wallet.balance + amount);
      await walletsCollection.doc(userId).set(updatedWallet.toJson());

      // Create transaction record
      final transaction = wallet_transaction.Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        amount: amount,
        type: wallet_transaction.TransactionType.deposit,
        description: description,
        methodType: 'Manual Add',
        timestamp: DateTime.now(),
      );

      await transactionsCollection
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      throw Exception("Failed to add balance: $e");
    }
  }

  @override
  Future<List<wallet_transaction.Transaction>> getTransactions(
    String userId,
  ) async {
    try {
      final transactionsSnapshot = await transactionsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .get();

      return transactionsSnapshot.docs
          .map(
            (doc) => wallet_transaction.Transaction.fromJson(
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (e) {
      throw Exception("Failed to get transactions: $e");
    }
  }

  @override
  Future<List<wallet_transaction.Transaction>> getTransactionsByType(
    String userId,
    wallet_transaction.TransactionType type,
  ) async {
    try {
      // Get all transactions for user first, then filter by type
      final transactionsSnapshot = await transactionsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .get();

      final allTransactions = transactionsSnapshot.docs
          .map(
            (doc) => wallet_transaction.Transaction.fromJson(
              doc.data() as Map<String, dynamic>,
            ),
          )
          .toList();

      // Filter by type in memory to avoid composite index requirement
      return allTransactions
          .where((transaction) => transaction.type == type)
          .toList();
    } catch (e) {
      throw Exception("Failed to get transactions by type: $e");
    }
  }

  // add transaction
  @override
  Future<void> addTransaction(
    wallet_transaction.Transaction transaction,
  ) async {
    try {
      await transactionsCollection
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      throw Exception("Failed to add transaction: $e");
    }
  }

  // remove transaction
  @override
  Future<void> removeTransaction(String transactionId) async {
    try {
      await transactionsCollection.doc(transactionId).delete();
    } catch (e) {
      throw Exception("Failed to remove transaction: $e");
    }
  }

  @override
  Future<void> deductBalance(
    String userId,
    double amount,
    String description,
    String? methodType,
  ) async {
    try {
      // Get current wallet
      final wallet = await getWallet(userId);

      // Update balance
      final updatedWallet = wallet.copyWith(balance: wallet.balance - amount);
      await walletsCollection.doc(userId).set(updatedWallet.toJson());

      // Create transaction record
      final transaction = wallet_transaction.Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        amount: amount,
        type: wallet_transaction.TransactionType.spent,
        description: description,
        methodType: methodType ?? 'Manual Deduct',
        timestamp: DateTime.now(),
      );

      await transactionsCollection
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      throw Exception("Failed to deduct balance: $e");
    }
  }
}
