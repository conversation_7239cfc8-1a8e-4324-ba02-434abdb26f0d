import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_tile.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/pages/upload_post_page.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/pages/profile_page.dart';
import 'package:social_app_bloc_flutter/features/search/presentation/cubits/pages/search_page.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/pages/wallet_page.dart';
import 'package:social_app_bloc_flutter/features/settings/pages/settings_page.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  late final postCubit = context.read<PostCubit>();
  late final profileCubit = context.read<ProfileCubit>();
  late TabController _tabController;
  int _selectedIndex = 0;
  PostCategory? userSelectedCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // fetch user profile to get selected category
    fetchUserProfile();
    // fetch all posts
    fetchAllPosts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void fetchUserProfile() async {
    final user = context.read<AuthCubit>().currentUser;
    if (user != null) {
      await profileCubit.fetchUserProfile(user.uid);
    }
  }

  void fetchAllPosts() async {
    await postCubit.fetchAllPosts();
  }

  void fetchPostsByCategory(PostCategory category) async {
    await postCubit.fetchPostsByCategory(category);
  }

  // delete post
  void deletePost(String postId) async {
    await postCubit.deletePost(postId);
    fetchAllPosts();
  }

  // Build home feed with tab bar
  Widget _buildHomeFeed() {
    return BlocBuilder<ProfileCubit, ProfileStates>(
      builder: (context, profileState) {
        if (profileState is ProfileLoaded) {
          userSelectedCategory = profileState.profileUser.selectedPostCategory;
        }

        return DefaultTabController(
          length: 2,
          child: Column(
            children: [
              // Tab Bar
              TabBar(
                controller: _tabController,
                dividerColor: Colors.transparent,
                labelColor: Theme.of(context).colorScheme.inversePrimary,
                unselectedLabelColor: Theme.of(context).colorScheme.primary,
                onTap: (index) {
                  if (index == 0 && userSelectedCategory != null) {
                    // Category tab - fetch posts by user's selected category
                    fetchPostsByCategory(userSelectedCategory!);
                  } else if (index == 1) {
                    // All posts tab
                    fetchAllPosts();
                  }
                },
                tabs: [
                  Tab(
                    text: userSelectedCategory != null
                        ? userSelectedCategory!.name
                        : 'CATEGORY',
                  ),
                  const Tab(text: 'ALL'),
                ],
              ),

              // Tab Bar View
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPostsList(), // Category posts
                    _buildPostsList(), // All posts
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build posts list widget
  Widget _buildPostsList() {
    return BlocBuilder<PostCubit, PostStates>(
      builder: (context, state) {
        // loading...
        if (state is PostsLoading || state is PostsUploading) {
          return const Center(child: CircularProgressIndicator());
        } // loaded
        else if (state is PostsLoaded) {
          final allPosts = state.posts;
          if (allPosts.isEmpty) {
            return const Center(child: Text('No posts found...'));
          }

          return ListView.builder(
            itemCount: allPosts.length,
            itemBuilder: (context, index) {
              final post = allPosts[index];

              return PostTile(
                post: post,
                onDeletePressed: () => deletePost(post.id),
              );
            },
          );
        } else if (state is PostsError) {
          // error
          return Center(child: Text(state.message));
        } else {
          return const SizedBox();
        }
      },
    );
  }

  // Build current page based on selected index
  Widget _buildCurrentPage() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeFeed();
      case 1:
        return const SearchPage();
      case 2:
        return const UploadPostPage();
      case 3:
        return const WalletPage();
      case 4:
        final user = context.read<AuthCubit>().currentUser;
        String? uid = user!.uid;
        return ProfilePage(uid: uid);
      case 5:
        return const SettingsPage();
      default:
        return _buildHomeFeed();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      body: _buildCurrentPage(),

      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(blurRadius: 20, color: Colors.black.withAlpha(25)),
          ],
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8),
            child: GNav(
              tabBorderRadius: 20,
              rippleColor: Theme.of(context).colorScheme.tertiary.withAlpha(25),
              hoverColor: Theme.of(context).colorScheme.tertiary.withAlpha(13),
              gap: 4,
              activeColor: Theme.of(
                context,
              ).colorScheme.tertiary.withAlpha(200),
              iconSize: 18,
              textSize: 16,

              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              duration: const Duration(milliseconds: 300),
              tabBackgroundColor: Theme.of(
                context,
              ).colorScheme.primary.withAlpha(25),
              color: Theme.of(context).colorScheme.primary,
              tabs: const [
                GButton(icon: Icons.home, text: 'Home'),
                GButton(icon: Icons.search, text: 'Search'),
                GButton(icon: Icons.add_circle_outline, text: 'Create'),
                GButton(icon: Icons.account_balance_wallet, text: 'Wallet'),
                GButton(icon: Icons.person, text: 'Profile'),
                GButton(icon: Icons.settings, text: 'Settings'),
              ],
              selectedIndex: _selectedIndex,
              onTabChange: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
            ),
          ),
        ),
      ),
    );
  }
}
