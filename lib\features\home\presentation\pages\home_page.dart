import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_tile.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/pages/upload_post_page.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/pages/profile_page.dart';
import 'package:social_app_bloc_flutter/features/search/presentation/cubits/pages/search_page.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/pages/wallet_page.dart';
import 'package:social_app_bloc_flutter/features/settings/pages/settings_page.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late final postCubit = context.read<PostCubit>();
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // fetch all posts
    fetchAllPosts();
  }

  void fetchAllPosts() async {
    await postCubit.fetchAllPosts();
  }

  // delete post
  void deletePost(String postId) async {
    await postCubit.deletePost(postId);
    fetchAllPosts();
  }

  // Build home feed widget
  Widget _buildHomeFeed() {
    return BlocBuilder<PostCubit, PostStates>(
      builder: (context, state) {
        // loading...
        if (state is PostsLoading || state is PostsUploading) {
          return const Center(child: CircularProgressIndicator());
        } // loaded
        else if (state is PostsLoaded) {
          final allPosts = state.posts;
          if (allPosts.isEmpty) {
            return const Center(child: Text('No posts found...'));
          }

          return ListView.builder(
            itemCount: allPosts.length,
            itemBuilder: (context, index) {
              final post = allPosts[index];

              return PostTile(
                post: post,
                onDeletePressed: () => deletePost(post.id),
              );
            },
          );
        } else if (state is PostsError) {
          // error
          return Center(child: Text(state.message));
        } else {
          return const SizedBox();
        }
      },
    );
  }

  // Build current page based on selected index
  Widget _buildCurrentPage() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeFeed();
      case 1:
        return const SearchPage();
      case 2:
        return const UploadPostPage();
      case 3:
        return const WalletPage();
      case 4:
        final user = context.read<AuthCubit>().currentUser;
        String? uid = user!.uid;
        return ProfilePage(uid: uid);
      case 5:
        return const SettingsPage();
      default:
        return _buildHomeFeed();
    }
  }

  // Get app bar title based on selected index
  String _getAppBarTitle() {
    switch (_selectedIndex) {
      case 0:
        return 'Home';
      case 1:
        return 'Search';
      case 2:
        return 'Create Post';
      case 3:
        return 'Wallet';
      case 4:
        return 'Profile';
      case 5:
        return 'Settings';
      default:
        return 'Home';
    }
  }

  // Get app bar actions based on selected index
  List<Widget>? _getAppBarActions() {
    switch (_selectedIndex) {
      case 4:
        return [
          IconButton(
            onPressed: () {
              context.read<AuthCubit>().logout();
            },
            icon: const Icon(Icons.logout),
          ),
        ];
      default:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(_getAppBarTitle()),
        foregroundColor: Theme.of(context).colorScheme.primary,
        actions: _getAppBarActions(),
      ),
      body: _buildCurrentPage(),

      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(blurRadius: 20, color: Colors.black.withAlpha(25)),
          ],
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8),
            child: GNav(
              tabBorderRadius: 20,
              rippleColor: Theme.of(context).colorScheme.tertiary.withAlpha(25),
              hoverColor: Theme.of(context).colorScheme.tertiary.withAlpha(13),
              gap: 4,
              activeColor: Theme.of(
                context,
              ).colorScheme.tertiary.withAlpha(200),
              iconSize: 18,
              textSize: 16,

              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              duration: const Duration(milliseconds: 300),
              tabBackgroundColor: Theme.of(
                context,
              ).colorScheme.primary.withAlpha(25),
              color: Theme.of(context).colorScheme.primary,
              tabs: const [
                GButton(icon: Icons.home, text: 'Home'),
                GButton(icon: Icons.search, text: 'Search'),
                GButton(icon: Icons.add_circle_outline, text: 'Create'),
                GButton(icon: Icons.account_balance_wallet, text: 'Wallet'),
                GButton(icon: Icons.person, text: 'Profile'),
                GButton(icon: Icons.settings, text: 'Settings'),
              ],
              selectedIndex: _selectedIndex,
              onTabChange: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
            ),
          ),
        ),
      ),
    );
  }
}
