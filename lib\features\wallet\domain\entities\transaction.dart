import 'package:cloud_firestore/cloud_firestore.dart';

enum TransactionType { deposit, spent }

class Transaction {
  final String id;
  final String userId;
  final double amount;
  final TransactionType type;
  final String description;
  final DateTime timestamp;

  Transaction({
    required this.id,
    required this.userId,
    required this.amount,
    required this.type,
    required this.description,
    required this.timestamp,
  });

  // Convert transaction to Json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'amount': amount,
      'type': type.name,
      'description': description,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }

  // Convert Json to transaction
  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      userId: json['userId'],
      amount: json['amount'].toDouble(),
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      description: json['description'],
      timestamp: (json['timestamp'] as Timestamp).toDate(),
    );
  }
}
