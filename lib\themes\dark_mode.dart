import 'package:flutter/material.dart';

ThemeData darkMode = ThemeData(
  colorScheme: const ColorScheme.dark(
    // very dark - app bar + drawer color
    surface: Color.fromARGB(255, 9, 9, 9),

    // slightly light
    primary: Color.fromARGB(255, 105, 105, 105),

    // dark
    secondary: Color.fromARGB(255, 20, 20, 20),

    // slightly dark
    tertiary: Color(0xFF5F66FF),

    // very light
    inversePrimary: Color.fromARGB(255, 195, 195, 195),

    // Surface tint
    surfaceTint: Colors.blue,
  ),
  scaffoldBackgroundColor: const Color.fromARGB(255, 9, 9, 9),
);
