import 'dart:io';
import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/common/components/SnackBar.dart';
import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/components/my_text_field.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';

class UploadPostPage extends StatefulWidget {
  const UploadPostPage({super.key});

  @override
  State<UploadPostPage> createState() => _UploadPostPageState();
}

class _UploadPostPageState extends State<UploadPostPage> {
  // mobile Image picker
  PlatformFile? imagePickedFile;
  // web Image picker
  Uint8List? webImage;
  // text controller for post text
  final textController = TextEditingController();
  // selected category
  PostCategory selectedCategory = PostCategory.politics;

  // current user
  AppUser? currentUser;

  @override
  void initState() {
    super.initState();
    // get current user
    getCurrentUser();
  }

  // get current user
  Future<void> getCurrentUser() async {
    final authCubit = context.read<AuthCubit>();
    currentUser = authCubit.currentUser;
  }

  // select image
  // -----------------------------
  Future<void> pickImage() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      // withData: kIsWeb,
    );
    if (result != null) {
      // mobile
      imagePickedFile = result.files.first;
      if (kIsWeb) {
        webImage = imagePickedFile!.bytes;
      }
      setState(() {});
    }
  }

  // create & upload post
  void uploadPost() {
    // check if both image and caption are provided
    if (imagePickedFile == null || textController.text.isEmpty) {
      showSnack(context, 'Please provide both image and caption');
      return;
    }
    // create new post object
    final newPost = Post(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: currentUser!.uid,
      userName: currentUser!.name,
      text: textController.text,
      imageUrl: '',
      timestamp: DateTime.now(),
      likes: [],
      comments: [],
      category: selectedCategory,
    );

    // upload post
    final postCubit = context.read<PostCubit>();

    // web upload
    if (kIsWeb) {
      postCubit.createPost(newPost, imageBytes: imagePickedFile!.bytes);
    }
    // mobile upload
    else {
      postCubit.createPost(newPost, imagePath: imagePickedFile!.path);
    }
  }

  @override
  dispose() {
    textController.dispose();
    super.dispose();
  }

  // BUILD UI
  @override
  Widget build(BuildContext context) {
    // BLOC CONSUMER -> builder + listener
    return BlocConsumer<PostCubit, PostStates>(
      builder: (context, state) {
        // loading or uploading
        if (state is PostsLoading || state is PostsUploading) {
          return ConstrainedScaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  Text('Uploading...'),
                ],
              ),
            ),
          );
        }
        // error
        else if (state is PostsError) {
          return ConstrainedScaffold(body: Center(child: Text(state.message)));
        }
        // loaded
        else {
          return buildUploadPage();
        }
      },
      listener: (context, state) {
        if (state is PostsLoaded) {
          // navigate back to home page
          Navigator.pop(context);
        }
      },
    );
  }

  Widget buildUploadPage() {
    // SAFFOLD
    return ConstrainedScaffold(
      // APP BAR
      appBar: AppBar(
        title: Text("Create Post"),
        centerTitle: true,
        foregroundColor: Theme.of(context).colorScheme.primary,
        actions: [IconButton(onPressed: uploadPost, icon: Icon(Icons.upload))],
      ),
      body: Center(
        child: Column(
          children: [
            // Image Preview for web
            if (kIsWeb && webImage != null)
              Image.memory(webImage!, fit: BoxFit.cover),
            // Image Preview for mobile
            if (!kIsWeb && imagePickedFile != null)
              Image.file(File(imagePickedFile!.path!)),

            // pick image button
            MaterialButton(
              onPressed: pickImage,
              color: Theme.of(context).colorScheme.surfaceTint,
              child: Text('Pick Image'),
            ),

            // caption text box
            MyTextField(
              controller: textController,
              hintText: 'Caption',
              obscureText: false,
            ),

            const SizedBox(height: 20),

            // category dropdown
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 25.0),
              child: DropdownButtonFormField<PostCategory>(
                value: selectedCategory,
                decoration: InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                items: PostCategory.values.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(
                      category.name.toUpperCase(),
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  );
                }).toList(),
                onChanged: (PostCategory? newValue) {
                  if (newValue != null) {
                    setState(() {
                      selectedCategory = newValue;
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
