import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/entities/profile_user.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/repos/profile_repo.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/features/storage/domain/storage_repo.dart';

class ProfileCubit extends Cubit<ProfileStates> {
  final ProfileRepo profileRepo;
  final StorageRepo storageRepo;

  ProfileCubit({required this.profileRepo, required this.storageRepo})
    : super(ProfileInitial());

  // fetch user profile using repo
  Future<void> fetchUserProfile(String uid) async {
    try {
      emit(ProfileLoading());
      final user = await profileRepo.fetchUserProfile(uid);
      if (user != null) {
        emit(ProfileLoaded(user));
      } else {
        emit(ProfileError('User not found'));
      }
    } catch (e) {
      emit(ProfileError("Failed to fetch user: $e"));
    }
  }

  // get user profile on given id -> useful for loading many profiles
  Future<ProfileUser?> getUserProfile(String uid) async {
    try {
      final user = await profileRepo.fetchUserProfile(uid);
      return user;
    } catch (e) {
      return null;
    }
  }

  // update bio and profile picture

  Future<void> updateProfile({
    required String uid,
    String? newBio,
    Uint8List? imageWebBytes,
    String? imageMobilePath,
    PostCategory? newSelectedPostCategory,
  }) async {
    emit(ProfileLoading());

    try {
      // fetch currrent user
      final currentUser = await profileRepo.fetchUserProfile(uid);

      if (currentUser == null) {
        emit(ProfileError('Failed to fetch user for Profile update'));
        return;
      }
      // profile picture update
      String? imageDownloadUrl;
      // ensure there is an image to upload
      if (imageWebBytes != null || imageMobilePath != null) {
        // for mobile
        if (imageMobilePath != null) {
          // upload
          imageDownloadUrl = await storageRepo.uploadProfileImageMobile(
            imageMobilePath,
            uid,
          );
          // for web
        } else if (imageWebBytes != null) {
          // upload
          imageDownloadUrl = await storageRepo.uploadProfileImageWeb(
            imageWebBytes,
            uid,
          );
        }
        if (imageDownloadUrl == null) {
          emit(ProfileError('Failed to upload image'));
          return;
        }
      }
      //update
      final updatedProfile = currentUser.copyWith(
        newBio: newBio ?? currentUser.bio,
        newProfileImageUrl: imageDownloadUrl ?? currentUser.profileImageUrl,
        newSelectedPostCategory:
            newSelectedPostCategory ?? currentUser.selectedPostCategory,
      );

      // update in repo

      await profileRepo.updateProfile(updatedProfile);

      // re-fetch the updated Profile
      await fetchUserProfile(uid);
    } catch (e) {
      emit(ProfileError('Error updating Profile: $e'));
    }
  }

  // toggle follow/unfollow
  Future<void> toggleFollow(String currentUid, String targetUid) async {
    try {
      // update in repo
      await profileRepo.toggleFollow(currentUid, targetUid);
    } catch (e) {
      emit(ProfileError('Failed to toggle follow: $e'));
    }
  }
}
