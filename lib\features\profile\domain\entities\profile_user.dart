import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';

class ProfileUser extends AppUser {
  final String bio;
  final String profileImageUrl;
  final List<String> following;
  final List<String> followers;
  final PostCategory selectedPostCategory;

  ProfileUser({
    required this.bio,
    required this.profileImageUrl,
    required super.uid,
    required super.name,
    required super.email,
    required this.following,
    required this.followers,
    required this.selectedPostCategory,
  });

  // Method to update profile user
  ProfileUser copyWith({
    String? newBio,
    String? newProfileImageUrl,
    List<String>? newFollowing,
    List<String>? newFollowers,
    PostCategory? newSelectedPostCategory,
  }) {
    return ProfileUser(
      uid: uid,
      name: name,
      email: email,
      bio: newBio ?? bio,
      profileImageUrl: newProfileImageUrl ?? profileImageUrl,
      following: newFollowing ?? following,
      followers: newFollowers ?? followers,
      selectedPostCategory: newSelectedPostCategory ?? selectedPostCategory,
    );
  }

  // convert profile user ->  to <PERSON><PERSON>
  @override
  Map<String, dynamic> to<PERSON>son() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'bio': bio,
      'profileImageUrl': profileImageUrl,
      'following': following,
      'followers': followers,
      'selectedPostCategory': selectedPostCategory.name,
    };
  }

  // Convert Json to -> profile user
  factory ProfileUser.fromJson(Map<String, dynamic> json) {
    return ProfileUser(
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      bio: json['bio'] ?? '',
      profileImageUrl: json['profileImageUrl'] ?? '',
      following: List<String>.from(json['following'] ?? []),
      followers: List<String>.from(json['followers'] ?? []),
      selectedPostCategory: PostCategory.values.firstWhere(
        (e) => e.name == json['selectedPostCategory'],
        orElse: () => PostCategory.politics,
      ),
    );
  }
}
