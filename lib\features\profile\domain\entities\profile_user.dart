import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';

class ProfileUser extends AppUser {
  final String bio;
  final String profileImageUrl;
  // follow system
  final List<String> following;
  final List<String> followers;

  ProfileUser({
    required this.bio,
    required this.profileImageUrl,
    required super.uid,
    required super.name,
    required super.email,
    required this.following,
    required this.followers,
  });

  // Method to update profile user
  ProfileUser copyWith({
    String? newBio,
    String? newProfileImageUrl,
    List<String>? newFollowing,
    List<String>? newFollowers,
  }) {
    return ProfileUser(
      uid: uid,
      name: name,
      email: email,
      bio: newBio ?? bio,
      profileImageUrl: newProfileImageUrl ?? profileImageUrl,
      following: newFollowing ?? following,
      followers: newFollowers ?? followers,
    );
  }

  // convert profile user ->  to Json
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'bio': bio,
      'profileImageUrl': profileImageUrl,
      'following': following,
      'followers': followers,
    };
  }

  // Convert Json to -> profile user
  factory ProfileUser.fromJson(Map<String, dynamic> json) {
    return ProfileUser(
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      bio: json['bio'] ?? '',
      profileImageUrl: json['profileImageUrl'] ?? '',
      following: List<String>.from(json['following'] ?? []),
      followers: List<String>.from(json['followers'] ?? []),
    );
  }
}
