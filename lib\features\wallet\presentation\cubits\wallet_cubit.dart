import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/wallet/domain/entities/transaction.dart';
import 'package:social_app_bloc_flutter/features/wallet/domain/repos/wallet_repo.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_states.dart';

class WalletCubit extends Cubit<WalletStates> {
  final WalletRepo walletRepo;

  WalletCubit({required this.walletRepo}) : super(WalletInitial());

  // Load wallet and transactions
  Future<void> loadWallet(String userId) async {
    try {
      emit(WalletLoading());
      final wallet = await walletRepo.getWallet(userId);
      final transactions = await walletRepo.getTransactions(userId);
      emit(WalletLoaded(wallet: wallet, transactions: transactions));
    } catch (e) {
      emit(WalletError("Failed to load wallet: $e"));
    }
  }

  // Add balance
  Future<void> addBalance(
    String userId,
    double amount,
    String description,
  ) async {
    try {
      await walletRepo.addBalance(userId, amount, description);
      // Reload wallet data
      await loadWallet(userId);
    } catch (e) {
      emit(WalletError("Failed to add balance: $e"));
    }
  }

  // Get transactions by type (optimized to avoid extra queries)
  Future<void> loadTransactionsByType(
    String userId,
    TransactionType type,
  ) async {
    try {
      emit(WalletLoading());
      final wallet = await walletRepo.getWallet(userId);
      final transactions = await walletRepo.getTransactionsByType(userId, type);
      emit(WalletLoaded(wallet: wallet, transactions: transactions));
    } catch (e) {
      emit(WalletError("Failed to load transactions: $e"));
    }
  }
}
