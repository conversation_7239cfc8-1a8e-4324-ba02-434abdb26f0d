import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/components/my_text_field.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/comment_tile.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/entities/profile_user.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/pages/profile_page.dart';

class PostTile extends StatefulWidget {
  final Post post;
  final void Function()? onDeletePressed;
  const PostTile({
    super.key,
    required this.post,
    required this.onDeletePressed,
  });

  @override
  State<PostTile> createState() => _PostTileState();
}

class _PostTileState extends State<PostTile> {
  // cubits
  late final postCubit = context.read<PostCubit>();
  late final profile = context.read<ProfileCubit>();

  bool isOwnPost = false;
  // Current user
  AppUser? currentUser;

  // post user
  ProfileUser? postUser;

  @override
  void initState() {
    super.initState();
    // get current user
    getCurrrentUser();
    // fetch post user
    fetchPostUser();
  }

  void getCurrrentUser() async {
    final authCubit = context.read<AuthCubit>();
    currentUser = authCubit.currentUser;
    isOwnPost = currentUser?.uid == widget.post.userId;
  }

  void fetchPostUser() async {
    final fetchedUser = await profile.getUserProfile(widget.post.userId);
    if (fetchedUser != null) {
      setState(() {
        postUser = fetchedUser;
      });
    }
  }

  /* 
LIKES
*/
  // user taps like button
  void toggleLikePost() {
    // Current Like status
    final isLiked = widget.post.likes.contains(currentUser!.uid);
    // optimistically update the UI
    setState(() {
      if (isLiked) {
        widget.post.likes.remove(currentUser!.uid); // unlike
      } else {
        widget.post.likes.add(currentUser!.uid); // like
      }
    });
    postCubit.toggleLikePost(widget.post.id, currentUser!.uid).catchError((e) {
      // rollback the UI
      setState(() {
        if (isLiked) {
          widget.post.likes.add(currentUser!.uid); // like
        } else {
          widget.post.likes.remove(currentUser!.uid); // unlike
        }
      });
    });
  }

  /* 
COMMENTS
*/
  // comment text controller
  final commentController = TextEditingController();

  // open comment box  -> user wants to type a new comment

  void openNewCommentBox() {
    // clear text controller
    commentController.clear();
    // show dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: MyTextField(
          controller: commentController,
          hintText: 'Type your comment...',
          obscureText: false,
        ),
        actions: [
          // cancel button
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          // submit button
          TextButton(
            onPressed: () {
              addComment();
              Navigator.of(context).pop();
            },
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }

  // add comment
  void addComment() {
    final newComment = Comment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      postId: widget.post.id,
      userId: currentUser!.uid,
      userName: currentUser!.name,
      text: commentController.text,
      timestamp: DateTime.now(),
    );

    if (commentController.text.isEmpty) {
      return;
    }
    postCubit.addComment(widget.post.id, newComment);
    commentController.clear();
  }

  // show options for deletion
  void showOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Options'),
        content: Text('Are you sure you want to delete this post?'),
        actions: [
          // delete button
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDeletePressed!();
            },
            child: Text('Delete', style: TextStyle(color: Colors.red)),
          ),
          // cancel button
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    commentController.dispose();
    super.dispose();
  }

  // BUILD UI
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.tertiary,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Top section of the post: profile pic/ name/ delete button
          GestureDetector(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ProfilePage(uid: widget.post.userId),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  // profile pic
                  postUser?.profileImageUrl != null
                      ? CachedNetworkImage(
                          imageUrl: postUser?.profileImageUrl ?? '',
                          fit: BoxFit.cover,
                          placeholder: (context, url) =>
                              const CircularProgressIndicator(),
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.person),
                          imageBuilder: (context, imageProvider) => Container(
                            height: 40,
                            width: 40,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        )
                      : Icon(
                          Icons.person,
                          color: Theme.of(context).colorScheme.primary,
                          size: 40,
                        ),
                  const SizedBox(width: 10),
                  // name
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.post.userName,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // date and category formatted
                      Row(
                        children: [
                          Text(
                            timeago.format(widget.post.timestamp),
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.outline,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '• ',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            widget.post.category.name,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.tertiary,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  //delete buttons
                  if (isOwnPost)
                    IconButton(
                      onPressed: showOptions,
                      icon: Icon(
                        Icons.more_vert,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                ],
              ),
            ),
          ),

          // image
          CachedNetworkImage(
            imageUrl: widget.post.imageUrl,
            imageBuilder: (context, imageProvider) => Container(
              height: 430,
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            fit: BoxFit.cover,
            placeholder: (context, url) =>
                const SizedBox(height: 430, width: double.infinity),
            errorWidget: (context, url, error) => const Icon(Icons.error),
          ),
          // CAPTION
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 10.0,
            ),
            child: Row(
              children: [
                // // user name
                // Text(
                //   widget.post.userName,
                //   style: TextStyle(fontWeight: FontWeight.bold),
                // ),
                // const SizedBox(width: 10),
                // caption text
                Text(widget.post.text),
              ],
            ),
          ),
          // buttons -> like, comment, timestamp
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              children: [
                SizedBox(
                  width: 50,
                  child: Row(
                    children: [
                      // like button
                      GestureDetector(
                        onTap: toggleLikePost,
                        child: Icon(
                          widget.post.likes.contains(currentUser!.uid)
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: widget.post.likes.contains(currentUser!.uid)
                              ? Colors.red
                              : Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        widget.post.likes.length.toString(),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                // comment button
                GestureDetector(
                  onTap: openNewCommentBox,
                  child: Icon(
                    Icons.comment_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 5),
                Text(
                  widget.post.comments.length.toString(),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () {},
                  child: Icon(
                    Icons.share_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),

          // COMMENT SECTION
          BlocBuilder<PostCubit, PostStates>(
            builder: (context, state) {
              // LOADED
              if (state is PostsLoaded) {
                // final individual post
                final post = state.posts.firstWhere(
                  (p) => p.id == widget.post.id,
                );
                if (post.comments.isNotEmpty) {
                  // How many comments to show
                  int showCommentsCount = post.comments.length;
                  // comment section
                  return ListView.builder(
                    shrinkWrap: true,
                    itemCount: showCommentsCount,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      // get comment individually
                      final comment = widget.post.comments[index];
                      return CommentTile(comment: comment);
                    },
                  );
                }
              }
              // LOADING...
              if (state is PostsLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is PostsError) {
                return Center(child: Text(state.message));
              } else {
                return const SizedBox();
              }
            },
          ),
        ],
      ),
    );
  }
}
