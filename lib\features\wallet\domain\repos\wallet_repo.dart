import 'package:social_app_bloc_flutter/features/wallet/domain/entities/transaction.dart';
import 'package:social_app_bloc_flutter/features/wallet/domain/entities/wallet.dart';

abstract class WalletRepo {
  Future<Wallet> getWallet(String userId);
  Future<void> addBalance(String userId, double amount, String description);
  Future<List<Transaction>> getTransactions(String userId);
  Future<List<Transaction>> getTransactionsByType(
    String userId,
    TransactionType type,
  );
  Future<void> addTransaction(Transaction transaction);
  Future<void> removeTransaction(String transactionId);
  // deduct balance
  Future<void> deductBalance(
    String userId,
    double amount,
    String description,
    String? methodType,
  );
}
