/*
Auth repository -  Outlines the possible auth operations for the app
*/

import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';

abstract class AuthRepo {
  Future<AppUser?> loginWithEmailAndPassword(String email, String password);
  Future<AppUser?> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  );
  Future<void> logout();
  Future<AppUser?> getCurrentUser();
}
