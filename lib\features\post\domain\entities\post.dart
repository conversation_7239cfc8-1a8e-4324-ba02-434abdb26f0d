import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';

enum PostCategory { news, politics, sex, entertainment, sports, religion }

class Post {
  final String id;
  final String userId;
  final String userName;
  //text
  final String text;
  final String imageUrl;
  // timestamp
  final DateTime timestamp;
  // likes
  final List<String> likes;
  // comments
  final List<Comment> comments;
  final PostCategory category;

  Post({
    required this.id,
    required this.userId,
    required this.userName,
    required this.text,
    required this.imageUrl,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.category,
  });

  Post copyWith({String? imageUrl}) {
    return Post(
      id: id,
      userId: userId,
      userName: userName,
      text: text,
      imageUrl: imageUrl ?? this.imageUrl,
      timestamp: timestamp,
      likes: likes,
      comments: comments,
      category: category,
    );
  }

  // convert post ->  to Json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'text': text,
      'imageUrl': imageUrl,
      'timestamp': Timestamp.fromDate(timestamp),
      'likes': likes,
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'category': category.name,
    };
  }

  factory Post.fromJson(Map<String, dynamic> json) {
    // Prepare comments
    final List<Comment> comments = (json['comments'] as List<dynamic>? ?? [])
        .map((commentJson) => Comment.fromJson(commentJson))
        .toList();
    return Post(
      id: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      text: json['text'],
      imageUrl: json['imageUrl'],
      timestamp: (json['timestamp'] as Timestamp).toDate(),
      likes: List<String>.from(json['likes'] ?? []),
      comments: comments,
      category: PostCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => PostCategory.politics,
      ),
    );
  }
}
