import 'package:social_app_bloc_flutter/features/wallet/domain/entities/transaction.dart';
import 'package:social_app_bloc_flutter/features/wallet/domain/entities/wallet.dart';

abstract class WalletStates {}

class WalletInitial extends WalletStates {}

class WalletLoading extends WalletStates {}

class WalletLoaded extends WalletStates {
  final Wallet wallet;
  final List<Transaction> transactions;

  WalletLoaded({required this.wallet, required this.transactions});
}

class WalletError extends WalletStates {
  final String message;

  WalletError(this.message);
}
