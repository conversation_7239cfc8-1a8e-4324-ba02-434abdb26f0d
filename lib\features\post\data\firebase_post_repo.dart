import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/domain/repos/post_repo.dart';

class FirebasePostRepo implements PostRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;

  // store the posts in the collection called 'posts_new'
  // final collectionName = 'posts_new';
  final CollectionReference postsCollection = FirebaseFirestore.instance
      .collection('posts_new');
  @override
  Future<void> createPost(Post post) async {
    try {
      await postsCollection.doc(post.id).set(post.toJson());
    } catch (e) {
      throw Exception("Failed to create post: $e");
    }
  }

  @override
  Future<void> deletePost(String postId) async {
    await postsCollection.doc(postId).delete();
  }

  @override
  Future<List<Post>> fetchAllPosts() async {
    try {
      // get all posts with most recent posts at the top
      final postsSnapshot = await postsCollection
          .orderBy('timestamp', descending: true)
          .get();
      if (postsSnapshot.docs.isEmpty) {
        return [];
      }
      // convert each  firestore document from json -> list of posts
      final List<Post> allPosts = postsSnapshot.docs
          .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
      return allPosts; // return all posts in the collection
    } catch (e) {
      throw Exception("Failed to fetch posts: $e");
    }
  }

  @override
  Future<List<Post>> fetchAllPostsByUserId(String userId) async {
    try {
      final postsSnapshot = await postsCollection
          .where('userId', isEqualTo: userId)
          // .orderBy('timestamp', descending: true)
          .get();
      if (postsSnapshot.docs.isEmpty) {
        return [];
      }
      // convert each  firestore document from json -> list of posts
      final List<Post> userPosts = postsSnapshot.docs
          .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
      return userPosts; // return all posts in the collection by user id
    } catch (e) {
      throw Exception("Failed to fetch posts: $e");
    }
  }

  @override
  Future<void> toggleLikePost(String postId, String userId) async {
    try {
      // get post document from firestore by post id
      final postDoc = await postsCollection.doc(postId).get();
      if (postDoc.exists) {
        final post = Post.fromJson(postDoc.data()! as Map<String, dynamic>);
        // check if user has liked the post already
        final hasLiked = post.likes.contains(userId);
        if (hasLiked) {
          post.likes.remove(userId); // unlike
        } else {
          post.likes.add(userId); // like
        }
        // update likes in firestore with the new likes list
        await postsCollection.doc(postId).update({'likes': post.likes});
      } else {
        throw Exception("Post Not Found"); // post not found in firestore
      }
    } catch (e) {
      throw Exception("Failed to toggle like post: $e");
    }
  }

  @override
  Future<void> addComment(String postId, Comment comment) async {
    try {
      // get the post document from firestore
      final postDoc = await postsCollection.doc(postId).get();
      if (postDoc.exists) {
        final post = Post.fromJson(postDoc.data()! as Map<String, dynamic>);
        post.comments.add(comment);
        // update comments in firestore with the new comments list
        await postsCollection.doc(postId).update({
          'comments': post.comments.map((comment) => comment.toJson()).toList(),
        });
      } else {
        throw Exception("Post Not Found"); // post not found in firestore
      }
    } catch (e) {
      throw Exception("Failed to add comment: $e");
    }
  }

  @override
  Future<void> deleteComment(String postId, String commentId) async {
    try {
      // get the post document from firestore
      final postDoc = await postsCollection.doc(postId).get();
      if (postDoc.exists) {
        final post = Post.fromJson(postDoc.data()! as Map<String, dynamic>);
        post.comments.removeWhere((comment) => comment.id == commentId);
        // update comments in firestore with the new comments list
        await postsCollection.doc(postId).update({
          'comments': post.comments.map((comment) => comment.toJson()).toList(),
        });
      } else {
        throw Exception("Post Not Found"); // post not found in firestore
      }
    } catch (e) {
      throw Exception("Failed to delete comment: $e");
    }
  }
}
