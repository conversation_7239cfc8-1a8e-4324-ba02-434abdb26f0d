import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_tile.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/components/bio_box.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/components/follow_button.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/components/profile_stats.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/pages/follower_page.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';

import '../../../auth/presentation/cubits/auth_cubit.dart';
import 'edit_profile_page.dart';

class ProfilePage extends StatefulWidget {
  final String uid;
  const ProfilePage({super.key, required this.uid});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late final authCubit = context.read<AuthCubit>();
  late final profileCubit = context.read<ProfileCubit>();

  // current user
  late AppUser? currentUser = authCubit.currentUser;

  // posts
  int postsCount = 0;

  // on startup
  @override
  void initState() {
    super.initState();
    profileCubit.fetchUserProfile(widget.uid);
  }

  // toggle follow
  void followButtonPressed() {
    final profileState = profileCubit.state;
    if (profileState is! ProfileLoaded) {
      return;
    }
    final profileUser = profileState.profileUser;
    final isFollowing = profileUser.followers.contains(currentUser!.uid);
    // optimistically update the UI
    if (isFollowing) {
      setState(() {
        profileUser.followers.remove(currentUser!.uid);
      });
    } else {
      setState(() {
        profileUser.followers.add(currentUser!.uid);
      });
    }
    profileCubit.toggleFollow(currentUser!.uid, widget.uid).catchError((e) {
      // rollback the UI
      setState(() {
        if (isFollowing) {
          // unfollow
          profileUser.followers.add(currentUser!.uid);
        } else {
          // follow
          profileUser.followers.remove(currentUser!.uid);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    // is Own Post
    bool isOwnPost = currentUser!.uid == widget!.uid;
    return BlocBuilder<ProfileCubit, ProfileStates>(
      builder: (context, state) {
        print("Profile State: $state");
        // loaded
        if (state is ProfileLoaded) {
          // get loaded user
          final user = state.profileUser;

          return ConstrainedScaffold(
            appBar: AppBar(
              centerTitle: true,
              title: Text(user.name),
              foregroundColor: Theme.of(context).colorScheme.primary,
              actions: [
                if (isOwnPost)
                  IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditProfilePage(user: user),
                        ),
                      );
                    },
                    icon: Icon(Icons.settings),
                  ),
              ],
            ),
            body: ListView(
              children: [
                // email
                Center(
                  child: Text(
                    user.email,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                // profile picture
                CachedNetworkImage(
                  imageUrl: user.profileImageUrl,

                  placeholder: (context, url) =>
                      Center(child: CircularProgressIndicator()),
                  errorWidget: (context, url, error) => Icon(
                    Icons.person,
                    size: 72,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  // Loaded
                  imageBuilder: (context, imageProvider) => Container(
                    height: 200,
                    width: 200,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceTint,
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        image: imageProvider,
                        fit: BoxFit.contain,
                      ),
                    ),
                    // clipBehavior: Clip.hardEdge,
                  ),
                ),
                const SizedBox(height: 25),

                // profile stats
                ProfileStats(
                  postCount: postsCount,
                  followerCount: user.followers.length,
                  followingCount: user.following.length,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => FollowerPage(
                          followers: user.followers,
                          following: user.following,
                        ),
                      ),
                    );
                  },
                ),

                // follow button
                if (!isOwnPost)
                  FollowButton(
                    onToggleFollow: followButtonPressed,
                    isFollowing: user.followers.contains(currentUser!.uid),
                  ),

                // bio Box
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Text(
                        "About me",
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                BioBox(text: user.bio),
                const SizedBox(height: 10),

                // list of posts from this user
                BlocBuilder<PostCubit, PostStates>(
                  builder: (context, state) {
                    if (state is PostsLoaded) {
                      final userPosts = state.posts
                          .where((p) => p.userId == widget.uid)
                          .toList();
                      postsCount = userPosts.length;
                      return Column(
                        children: [
                          if (userPosts.isNotEmpty)
                            Container(
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.outline,
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: Text(
                                "Posts",
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 20,
                                ),
                              ),
                            ),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: userPosts.length,
                            itemBuilder: (context, index) {
                              // get post individually
                              final post = userPosts[index];
                              return PostTile(
                                post: post,
                                onDeletePressed: () => context
                                    .read<PostCubit>()
                                    .deletePost(post.id),
                              );
                            },
                          ),
                        ],
                      );
                    } else if (state is PostsLoading) {
                      return const Center(child: CircularProgressIndicator());
                    } else {
                      return const Text('No posts found...');
                    }
                  },
                ),
              ],
            ),
          );
        }
        // loading ...
        else if (state is ProfileLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state is ProfileError) {
          return Center(
            child: Text('Error loading profile...${state.message}'),
          );
        }
        return const Center(child: Text('No profile found...'));
      },
    );
  }
}
