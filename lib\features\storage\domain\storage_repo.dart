import 'dart:typed_data';

abstract class StorageRepo {
  // upload Profile Images on Mobile Platforms
  Future<String?> uploadProfileImageMobile(String path, String fileName);

  // upload Profile Images on Web Platforms
  Future<String?> uploadProfileImageWeb(Uint8List fileBytes, String fileName);

  // upload Post Images on Mobile Platforms
  Future<String?> uploadPostImageMobile(String path, String fileName);

  // upload Post Images on Web Platforms
  Future<String?> uploadPostImageWeb(Uint8List fileBytes, String fileName);
}
