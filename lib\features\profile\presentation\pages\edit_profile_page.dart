import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/components/my_text_field.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';

import '../../../../common/components/SnackBar.dart';
import '../../domain/entities/profile_user.dart';

class EditProfilePage extends StatefulWidget {
  final ProfileUser user;

  const EditProfilePage({super.key, required this.user});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  // mobile Image picker
  PlatformFile? imagePickedFile;
  // web Image picker
  Uint8List? webImage;

  final bioTextController = TextEditingController();
  late PostCategory selectedCategory;

  @override
  void initState() {
    super.initState();
    selectedCategory = widget.user.selectedPostCategory;
  }

  // -----------------------------
  Future<void> pickImage() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      // withData: kIsWeb,
    );
    if (result != null) {
      // mobile
      imagePickedFile = result.files.first;
      if (kIsWeb) {
        webImage = imagePickedFile!.bytes;
      }
      setState(() {});
    }
  }

  void updateProfile() {
    // profile cubit
    final profileCubit = context.read<ProfileCubit>();
    // prepare images
    final String uid = widget.user.uid;
    final imageMobilePath = kIsWeb ? null : imagePickedFile?.path;
    final imageWebBytes = kIsWeb ? imagePickedFile!.bytes : null;

    // prepare bio
    final bioText = bioTextController.text.isNotEmpty
        ? bioTextController.text
        : null;
    // check if category changed
    final categoryChanged =
        selectedCategory != widget.user.selectedPostCategory;

    // only update profile if there is something to update
    if (imagePickedFile != null || bioText != null || categoryChanged) {
      profileCubit.updateProfile(
        uid: widget.user.uid,
        newBio: bioText,
        imageMobilePath: imageMobilePath,
        imageWebBytes: imageWebBytes,
        newSelectedPostCategory: categoryChanged ? selectedCategory : null,
      );
    } else {
      showSnack(context, 'Nothing to update');
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProfileCubit, ProfileStates>(
      builder: (context, state) {
        // profile loading
        if (state is ProfileLoading) {
          return ConstrainedScaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  Text('Uploading...'),
                ],
              ),
            ),
          );
        } else {
          //profile error
          if (state is ProfileError) {
            return ConstrainedScaffold(
              body: Center(child: Text(state.message)),
            );
          }
          //profile loaded, edit form
          return buildEditPage();
        }
      },
      listener: (context, state) {
        if (state is ProfileLoaded) {
          // navigate back to profile page
          Navigator.pop(context);
        }
      },
    );
  }

  Widget buildEditPage({String? bio}) {
    return ConstrainedScaffold(
      appBar: AppBar(
        title: const Text("Edit Profile"),
        centerTitle: true,
        foregroundColor: Theme.of(context).colorScheme.primary,
        actions: [
          // Save button
          IconButton(onPressed: updateProfile, icon: Icon(Icons.upload)),
        ],
      ),

      body: Column(
        children: [
          // profile picture
          Center(
            child: Container(
              height: 200,
              width: 200,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondary,
                shape: BoxShape.circle,
              ),
              clipBehavior: Clip.hardEdge,
              child:
                  //diplay selected image for mobile
                  (!kIsWeb && imagePickedFile != null)
                  ? Image.file(File(imagePickedFile!.path!))
                  :
                    //display selected image for web
                    (kIsWeb && webImage != null)
                  ? Image.memory(webImage!, fit: BoxFit.cover)
                  :
                    // display existing Profile Pic -> no image picked
                    CachedNetworkImage(
                      imageUrl: widget.user.profileImageUrl,

                      placeholder: (context, url) =>
                          CircularProgressIndicator(),
                      errorWidget: (context, url, error) => Icon(
                        Icons.person,
                        size: 72,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      // Loaded
                      imageBuilder: (context, imageProvider) => Container(
                        height: 200,
                        width: 200,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: imageProvider,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      fit: BoxFit.cover,
                    ),
            ),
          ),
          const SizedBox(height: 25),

          // pick image button
          Center(
            child: MaterialButton(
              onPressed: pickImage,
              color: Theme.of(context).colorScheme.tertiary,

              child: Text('Pick Image'),
            ),
          ),
          // bio
          Text("Bio"),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.all(25.0),
            child: MyTextField(
              controller: bioTextController,
              hintText: bio ?? 'Enter you bio...',
              obscureText: false,
            ),
          ),

          // category selection
          const SizedBox(height: 20),
          Text("Preferred Post Category"),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 25.0),
            child: DropdownButtonFormField<PostCategory>(
              value: selectedCategory,
              decoration: InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              items: PostCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(
                    category.name.toUpperCase(),
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                );
              }).toList(),
              onChanged: (PostCategory? newValue) {
                if (newValue != null) {
                  setState(() {
                    selectedCategory = newValue;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
