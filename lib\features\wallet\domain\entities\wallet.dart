class Wallet {
  final String userId;
  final double balance;

  Wallet({
    required this.userId,
    required this.balance,
  });

  // Convert wallet to Json
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'balance': balance,
    };
  }

  // Convert Json to wallet
  factory Wallet.fromJson(Map<String, dynamic> json) {
    return Wallet(
      userId: json['userId'],
      balance: json['balance'].toDouble(),
    );
  }

  // Copy with method for updating balance
  Wallet copyWith({double? balance}) {
    return Wallet(
      userId: userId,
      balance: balance ?? this.balance,
    );
  }
}
