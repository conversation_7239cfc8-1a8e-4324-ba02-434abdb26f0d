import 'package:social_app_bloc_flutter/features/profile/domain/entities/profile_user.dart';

/// Profile States
///

abstract class ProfileStates {}

// initial
class ProfileInitial extends ProfileStates {}

// loading...
class ProfileLoading extends ProfileStates {}

// loaded
class ProfileLoaded extends ProfileStates {
  final ProfileUser profileUser;
  ProfileLoaded(this.profileUser);
}

// error

class ProfileError extends ProfileStates {
  final String message;

  ProfileError(this.message);
}
