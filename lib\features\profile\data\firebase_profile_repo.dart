import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/entities/profile_user.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/repos/profile_repo.dart';

class FirebaseProfileRepo implements ProfileRepo {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;
  @override
  Future<ProfileUser?> fetchUserProfile(String uid) async {
    try {
      // get user document from firestore
      final userDoc = await firebaseFirestore
          .collection('users')
          .doc(uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data();
        if (userData != null) {
          // fetch following and followers from data
          final following = List<String>.from(userData['following'] ?? []);
          final followers = List<String>.from(userData['followers'] ?? []);

          return ProfileUser(
            uid: uid,
            name: userData['name'] ?? '',
            email: userData['email'],
            bio: userData['bio'] ?? '',
            profileImageUrl: userData['profileImageUrl'].toString(),
            following: following,
            followers: followers,
          );
        }
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  @override
  Future<void> updateProfile(ProfileUser updatedProfile) async {
    try {
      await firebaseFirestore
          .collection('users')
          .doc(updatedProfile.uid)
          .update({
            'bio': updatedProfile.bio,
            'profileImageUrl': updatedProfile.profileImageUrl,
          });
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<void> toggleFollow(String currentUid, String targetUid) async {
    try {
      // get current user document from firestore
      final currentUserDoc = await firebaseFirestore
          .collection('users')
          .doc(currentUid)
          .get();
      // get target user document from firestore
      final targetUserDoc = await firebaseFirestore
          .collection('users')
          .doc(targetUid)
          .get();

      if (currentUserDoc.exists && targetUserDoc.exists) {
        final currentUser = currentUserDoc.data();
        final targetUser = targetUserDoc.data();

        if (currentUser != null && targetUser != null) {
          final List<String> currentFollowing =
              (currentUser['following'] as List<dynamic>?)
                  ?.map((e) => e.toString())
                  .toList() ??
              [];

          // check if target user is already followed by current user
          final isFollowing = currentFollowing.contains(targetUid);

          if (isFollowing) {
            //unfollow
            await firebaseFirestore.collection('users').doc(currentUid).update({
              'following': FieldValue.arrayRemove([targetUid]),
            });
            await firebaseFirestore.collection('users').doc(targetUid).update({
              'followers': FieldValue.arrayRemove([currentUid]),
            });
          } else {
            // follow
            await firebaseFirestore.collection('users').doc(currentUid).update({
              'following': FieldValue.arrayUnion([targetUid]),
            });
            await firebaseFirestore.collection('users').doc(targetUid).update({
              'followers': FieldValue.arrayUnion([currentUid]),
            });
          }
        }
      }
    } catch (e) {
      throw Exception("Failed to toggle follow: $e");
    }
  }
}
